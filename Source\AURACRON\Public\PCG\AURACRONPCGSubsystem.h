// AURACRONPCGSubsystem.h
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Subsistema principal para gerenciar a geração procedural do mapa

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
// Forward declaration para evitar dependência direta
class UPCGComponent;
#include "PCGSettings.h"
#include "PCGPoint.h"
#include "PCGVolume.h"
#include "PCG/AURACRONPCGTypes.h"
#include "Data/AURACRONEnums.h"
#include "AURACRONPCGSubsystem.generated.h"

class UPCGComponent;
class APCGVolume;

// Usando EAURACRONTrailType definido em Data/AURACRONEnums.h

// Enum EAURACRONIslandType definido em Data/AURACRONEnums.h

/**
 * Subsistema para gerenciar a geração procedural do mapa AURACRON
 * Responsável por coordenar os diferentes ambientes, trilhas e o Prismal Flow
 */
UCLASS()
class AURACRON_API UAURACRONPCGSubsystem : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    UAURACRONPCGSubsystem();

    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    virtual bool ShouldCreateSubsystem(UObject* Outer) const override;

    // Funções para iniciar a geração do mapa
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void GenerateMap(FVector MapCenter, float MapRadius);

    // Funções para gerenciar as fases do mapa
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void AdvanceToNextPhase();

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetMapPhase(EAURACRONMapPhase NewPhase);

    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    EAURACRONMapPhase GetCurrentMapPhase() const { return CurrentMapPhase; }

    // Funções para gerenciar os ambientes
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void GenerateEnvironment(EAURACRONEnvironmentType EnvironmentType, FVector Center, float Radius);

    // Funções para gerenciar as trilhas
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void GenerateTrail(EAURACRONTrailType TrailType, TArray<FVector> ControlPoints);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void UpdateTrailPositions(float DeltaTime);

    // Funções para gerenciar o Prismal Flow
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void GeneratePrismalFlow(TArray<FVector> FlowControlPoints, float Width);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void UpdatePrismalFlow(float DeltaTime);

    // Funções para gerenciar as ilhas
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void GenerateIsland(EAURACRONIslandType IslandType, FVector Location, float Radius);

    /** Obter nível de qualidade atual */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Quality")
    int32 GetCurrentQualityLevel() const;

    // Métodos para integração com sistema de movimento
    /** Notificar quando personagem entra no fluxo prismal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Movement")
    void OnCharacterEnteredPrismalFlow(ACharacter* Character, FVector FlowDirection, float FlowSpeed);

    /** Notificar quando personagem sai do fluxo prismal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Movement")
    void OnCharacterExitedPrismalFlow(ACharacter* Character);

    /** Notificar mudança de estado de movimento do personagem */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Movement")
    void OnCharacterMovementStateChanged(ACharacter* Character, EAURACRONMovementState MovementState);

private:
    // Componentes PCG para cada ambiente
    UPROPERTY()
    TMap<EAURACRONEnvironmentType, UPCGComponent*> EnvironmentComponents;

    // Componentes PCG para as trilhas
    UPROPERTY()
    TMap<EAURACRONTrailType, UPCGComponent*> TrailComponents;

    // Componente PCG para o Prismal Flow
    UPROPERTY()
    UPCGComponent* PrismalFlowComponent;

    // Volumes PCG para cada ambiente
    UPROPERTY()
    TMap<EAURACRONEnvironmentType, APCGVolume*> EnvironmentVolumes;

    // Fase atual do mapa
    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;

    // Tempo decorrido desde o início da partida
    UPROPERTY()
    float ElapsedTime;

    // ✅ NOVAS PROPRIEDADES UE 5.6

    // StreamableManager para carregamento assíncrono
    UPROPERTY()
    class FStreamableManager* StreamableManager;

    // Timer para updates otimizados
    UPROPERTY()
    FTimerHandle UpdateTimerHandle;

    // Estado de inicialização
    UPROPERTY()
    bool bIsInitialized;

    // Qualidade atual detectada
    UPROPERTY()
    int32 CurrentQualityLevel;

    // Orçamento de partículas baseado na qualidade
    UPROPERTY()
    int32 ParticlesBudget;

    // Ilha Central Auracron conforme documentação
    UPROPERTY()
    class AAURACRONPCGNexusIsland* CentralAuracronIsland;

    // Assets carregados assincronamente
    UPROPERTY()
    TMap<EAURACRONEnvironmentType, class UPCGSettings*> LoadedPCGSettings;

    UPROPERTY()
    TMap<EAURACRONTrailType, class UNiagaraSystem*> LoadedNiagaraSystems;

    // ✅ FUNÇÕES INTERNAS ROBUSTAS
    void SetupPCGComponents();
    void UpdateEnvironmentBasedOnPhase();
    void UpdateTrailsBasedOnPhase();
    void UpdatePrismalFlowBasedOnPhase();

    // ✅ NOVAS FUNÇÕES UE 5.6
    void DetectAndSetQualityLevel();
    void SetupParticlesBudget();
    void SetupUpdateTimer();
    void SetupNetworking();
    void TimerUpdate();

    // Replicação
    UFUNCTION()
    void OnRep_CurrentMapPhase();

    // Notificações
    void NotifySystemsOfPhaseChange(EAURACRONMapPhase OldPhase, EAURACRONMapPhase NewPhase);

    // Criação de componentes
    void CreatePCGComponentForEnvironment(APCGVolume* Volume, EAURACRONEnvironmentType EnvironmentType);
    void UpdateExistingEnvironment(EAURACRONEnvironmentType EnvironmentType, FVector Center, float Radius);

    // Geração de trilhas conforme documentação
    void GenerateSolarTrails(FVector MapCenter, float MapRadius);
    void GenerateAxisTrails(FVector MapCenter, FVector ZephyrCenter, FVector PurgatoryCenter);
    void GenerateLunarTrails(FVector MapCenter, float MapRadius);

    // Geração do Fluxo Prismal conforme documentação
    TArray<FVector> GeneratePrismalFlowPath(FVector MapCenter, float MapRadius);
    float CalculateFlowWidth();

    // Geração da Ilha Central Auracron conforme documentação
    void GenerateCentralAuracronIsland(const TArray<FVector>& FlowControlPoints, FVector MapCenter);
    void GenerateStrategicIslands(const TArray<FVector>& FlowControlPoints, FVector MapCenter, float MapRadius);

    // APIs modernas PCG UE 5.6
    void UpdatePCGParametersModern(UPCGComponent* PCGComponent, const FString& ParameterName, float Value);
    void UpdatePCGParametersModern(UPCGComponent* PCGComponent, const FString& ParameterName, const FLinearColor& Value);

    // Funções auxiliares
    float GetPhaseIntensityMultiplier() const;
    FString GetPCGSettingsPathForEnvironment(EAURACRONEnvironmentType EnvironmentType) const;

    // ✅ FUNÇÕES ADICIONAIS UE 5.6

    // Carregamento assíncrono
    void LoadPCGSettingsAsync(UPCGComponent* PCGComponent, const FString& SettingsPath, EAURACRONEnvironmentType EnvironmentType);
    void OnPCGSettingsLoaded(UPCGComponent* PCGComponent, EAURACRONEnvironmentType EnvironmentType);
    void PreloadEssentialPCGAssets();
    void OnEssentialAssetsLoaded();
    void SetupObjectPools();

    // Criação de componentes específicos
    void CreatePCGComponentForTrail(APCGVolume* Volume, EAURACRONTrailType TrailType, const TArray<FVector>& ControlPoints);
    void CreatePCGComponentForPrismalFlow(APCGVolume* Volume, const TArray<FVector>& FlowControlPoints, float Width);
    void CreatePCGComponentForIsland(APCGVolume* Volume, EAURACRONIslandType IslandType, float Radius);

    // Carregamento assíncrono específico
    void LoadTrailPCGSettingsAsync(UPCGComponent* PCGComponent, const FString& SettingsPath, EAURACRONTrailType TrailType, const TArray<FVector>& ControlPoints);
    void LoadPrismalFlowPCGSettingsAsync(UPCGComponent* PCGComponent, const FString& SettingsPath, const TArray<FVector>& FlowControlPoints, float Width);
    void LoadIslandPCGSettingsAsync(UPCGComponent* PCGComponent, const FString& SettingsPath, EAURACRONIslandType IslandType, float Radius);

    // Caminhos de assets
    FString GetPCGSettingsPathForTrail(EAURACRONTrailType TrailType) const;
    FString GetPCGSettingsPathForIsland(EAURACRONIslandType IslandType) const;

    // Valores padrão
    float GetDefaultIslandRadius(EAURACRONIslandType IslandType) const;

    // Cálculos auxiliares
    float CalculateTrailLength(const TArray<FVector>& ControlPoints) const;
    float CalculateFlowVolatility() const;
    float CalculateFlowSpeed(float NormalizedTime) const;
    FLinearColor GetFlowColorForCurrentPhase() const;

    // ✅ FUNÇÕES FINAIS COMPLETAS UE 5.6

    // Multiplicadores de capacidade
    float GetDeviceCapacityMultiplier() const;
    float GetRenderingCapacityMultiplier() const;
    float GetEffectsScaleForDevice() const;
    float GetHardwareCapacityMultiplier() const;
    float GetAdaptiveIntensityForDevice() const;

    // Configuração de trilhas e ambientes
    void SetEnvironmentActiveState(EAURACRONEnvironmentType EnvironmentType, bool bActive, float Intensity);
    void SetTrailIntensity(EAURACRONTrailType TrailType, float Intensity);
    void EnableTrailIntersections(bool bEnable);
    void EnableTrailConvergence(float EffectsScale);

    // Aplicação de efeitos
    void ApplyTerrainChangesForPhase();
    void ApplyFinalConvergenceEffects();
    void NotifyEnvironmentSystemsOfPhaseChange();
    void ApplyParticlesBudgetToTrails();
    void ApplyParticlesBudgetToPrismalFlow();

    // Integração com movimento
    void ApplyPrismalFlowVFXToCharacter(ACharacter* Character, FVector FlowDirection, float FlowSpeed);
    void RemovePrismalFlowVFXFromCharacter(ACharacter* Character);
    void NotifySystemsOfCharacterFlowEntry(ACharacter* Character, FVector FlowDirection, float FlowSpeed);
    void NotifySystemsOfCharacterFlowExit(ACharacter* Character);
    void NotifySystemsOfMovementStateChange(ACharacter* Character, EAURACRONMovementState MovementState);

    // Handlers de estado de movimento
    void HandleNormalMovementState(ACharacter* Character);
    void HandlePrismalFlowMovementState(ACharacter* Character);
    void HandleSigilDashMovementState(ACharacter* Character);
    void HandleEnvironmentBoostMovementState(ACharacter* Character);
    void HandleStunnedMovementState(ACharacter* Character);
    void HandleRootedMovementState(ACharacter* Character);

    // Funções auxiliares finais
    FVector CalculateFlowDirectionAtLocation(FVector Location) const;
    EAURACRONEnvironmentType DetermineEnvironmentAtLocation(FVector Location) const;
};